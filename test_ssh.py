#!/usr/bin/env python3
"""
Simple script to test SSH connection to the deployment server
"""
import subprocess
import os

server_user = "citbank"
server_ip = "************"
server_port = "2018"
server_password = os.getenv('DEPLOY_PASSWORD', "tmxKnMHMzc7NJHAyJU2w94wVul6pw41FeJltciFV8pu8ctm")

def test_ssh_connection():
    """Test SSH connection to the server"""
    print("Testing SSH connection...")
    test_cmd = [
        "sshpass", "-p", server_password,
        "ssh", "-p", server_port,
        f"{server_user}@{server_ip}",
        "echo 'SSH connection successful' && whoami && pwd"
    ]
    
    try:
        result = subprocess.run(test_cmd, check=True, capture_output=True, text=True)
        print("✅ SSH connection test passed")
        print("Server response:")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ SSH connection test failed: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

if __name__ == "__main__":
    test_ssh_connection()
